#!/usr/bin/env python3
"""
Test script to verify that all libraries and code work correctly
"""

# Import libraries necessary for this project
import numpy as np
import pandas as pd
from sklearn.model_selection import ShuffleSplit

# Import supplementary visualizations code visuals.py
import visuals as vs

print("✓ All libraries imported successfully!")

# Load the Boston housing dataset
try:
    data = pd.read_csv('housing.csv')
    prices = data['MEDV']
    features = data.drop('MEDV', axis = 1)
    
    # Success
    print(f"✓ Boston housing dataset has {data.shape[0]} data points with {data.shape[1]} variables each.")
    print(f"✓ Features: {list(features.columns)}")
    print(f"✓ Price range: ${prices.min():,.2f} - ${prices.max():,.2f}")
    
except Exception as e:
    print(f"✗ Error loading data: {e}")

# Test visuals functions
try:
    print("\n✓ Testing visualization functions...")
    
    # Test ModelLearning function
    print("  - Testing ModelLearning function...")
    vs.ModelLearning(features, prices)
    print("  ✓ ModelLearning function works!")
    
    # Test ModelComplexity function  
    print("  - Testing ModelComplexity function...")
    vs.ModelComplexity(features, prices)
    print("  ✓ ModelComplexity function works!")
    
    print("\n🎉 All tests passed! The code is working correctly.")
    
except Exception as e:
    print(f"✗ Error in visualization functions: {e}")
    import traceback
    traceback.print_exc()
