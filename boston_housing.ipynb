{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Machine Learning Engineer <PERSON><PERSON>\n", "## Model Evaluation & Validation\n", "## Project: Predicting Boston Housing Prices\n", "\n", "Welcome to the first project of the Machine Learning Engineer Nanodegree! In this notebook, some template code has already been provided for you, and you will need to implement additional functionality to successfully complete this project. You will not need to modify the included code beyond what is requested. Sections that begin with **'Implementation'** in the header indicate that the following block of code will require additional functionality which you must provide. Instructions will be provided for each section and the specifics of the implementation are marked in the code block with a 'TODO' statement. Please be sure to read the instructions carefully!\n", "\n", "In addition to implementing code, there will be questions that you must answer which relate to the project and your implementation. Each section where you will answer a question is preceded by a **'Question X'** header. Carefully read each question and provide thorough answers in the following text boxes that begin with **'Answer:'**. Your project submission will be evaluated based on your answers to each of the questions and the implementation you provide.  \n", "\n", ">**Note:** Code and Markdown cells can be executed using the **Shift + Enter** keyboard shortcut. In addition, Markdown cells can be edited by typically double-clicking the cell to enter edit mode."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Getting Started\n", "In this project, you will evaluate the performance and predictive power of a model that has been trained and tested on data collected from homes in suburbs of Boston, Massachusetts. A model trained on this data that is seen as a *good fit* could then be used to make certain predictions about a home — in particular, its monetary value. This model would prove to be invaluable for someone like a real estate agent who could make use of such information on a daily basis.\n", "\n", "The dataset for this project originates from the [UCI Machine Learning Repository](https://archive.ics.uci.edu/ml/datasets/Housing). The Boston housing data was collected in 1978 and each of the 506 entries represent aggregated data about 14 features for homes from various suburbs in Boston, Massachusetts. For the purposes of this project, the following preprocessing steps have been made to the dataset:\n", "- 16 data points have an `'MEDV'` value of 50.0. These data points likely contain **missing or censored values** and have been removed.\n", "- 1 data point has an `'RM'` value of 8.78. This data point can be considered an **outlier** and has been removed.\n", "- The features `'RM'`, `'LSTAT'`, `'PTRATIO'`, and `'MEDV'` are essential. The remaining **non-relevant features** have been excluded.\n", "- The feature `'MEDV'` has been **multiplicatively scaled** to account for 35 years of market inflation.\n", "\n", "Run the code cell below to load the Boston housing dataset, along with a few of the necessary Python libraries required for this project. You will know the dataset loaded successfully if the size of the dataset is reported."]}, {"cell_type": "code", "execution_count": 39, "metadata": {"scrolled": true}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Boston housing dataset has 489 data points with 4 variables each.\n"]}], "source": ["# Import libraries necessary for this project\n", "import numpy as np\n", "import pandas as pd\n", "from sklearn.cross_validation import ShuffleSplit\n", "\n", "# Import supplementary visualizations code visuals.py\n", "import visuals as vs\n", "\n", "# Pretty display for notebooks\n", "%matplotlib inline\n", "\n", "# Load the Boston housing dataset\n", "data = pd.read_csv('housing.csv')\n", "prices = data['MEDV']\n", "features = data.drop('MEDV', axis = 1)\n", "    \n", "# Success\n", "print(\"Boston housing dataset has {} data points with {} variables each.\".format(*data.shape))"]}, {"cell_type": "code", "execution_count": 40, "metadata": {"scrolled": false}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>RM</th>\n", "      <th>LSTAT</th>\n", "      <th>PTRATIO</th>\n", "      <th>MEDV</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>6.575</td>\n", "      <td>4.98</td>\n", "      <td>15.3</td>\n", "      <td>504000.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>6.421</td>\n", "      <td>9.14</td>\n", "      <td>17.8</td>\n", "      <td>453600.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>7.185</td>\n", "      <td>4.03</td>\n", "      <td>17.8</td>\n", "      <td>728700.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>6.998</td>\n", "      <td>2.94</td>\n", "      <td>18.7</td>\n", "      <td>701400.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>7.147</td>\n", "      <td>5.33</td>\n", "      <td>18.7</td>\n", "      <td>760200.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>6.430</td>\n", "      <td>5.21</td>\n", "      <td>18.7</td>\n", "      <td>602700.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>6.012</td>\n", "      <td>12.43</td>\n", "      <td>15.2</td>\n", "      <td>480900.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>6.172</td>\n", "      <td>19.15</td>\n", "      <td>15.2</td>\n", "      <td>569100.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>5.631</td>\n", "      <td>29.93</td>\n", "      <td>15.2</td>\n", "      <td>346500.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>6.004</td>\n", "      <td>17.10</td>\n", "      <td>15.2</td>\n", "      <td>396900.0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["      RM  LSTAT  PTRATIO      MEDV\n", "0  6.575   4.98     15.3  504000.0\n", "1  6.421   9.14     17.8  453600.0\n", "2  7.185   4.03     17.8  728700.0\n", "3  6.998   2.94     18.7  701400.0\n", "4  7.147   5.33     18.7  760200.0\n", "5  6.430   5.21     18.7  602700.0\n", "6  6.012  12.43     15.2  480900.0\n", "7  6.172  19.15     15.2  569100.0\n", "8  5.631  29.93     15.2  346500.0\n", "9  6.004  17.10     15.2  396900.0"]}, "execution_count": 40, "metadata": {}, "output_type": "execute_result"}], "source": ["data[:10]"]}, {"cell_type": "code", "execution_count": 41, "metadata": {"scrolled": true}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>RM</th>\n", "      <th>LSTAT</th>\n", "      <th>PTRATIO</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>6.575</td>\n", "      <td>4.98</td>\n", "      <td>15.3</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>6.421</td>\n", "      <td>9.14</td>\n", "      <td>17.8</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>7.185</td>\n", "      <td>4.03</td>\n", "      <td>17.8</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>6.998</td>\n", "      <td>2.94</td>\n", "      <td>18.7</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>7.147</td>\n", "      <td>5.33</td>\n", "      <td>18.7</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>6.430</td>\n", "      <td>5.21</td>\n", "      <td>18.7</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>6.012</td>\n", "      <td>12.43</td>\n", "      <td>15.2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>6.172</td>\n", "      <td>19.15</td>\n", "      <td>15.2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>5.631</td>\n", "      <td>29.93</td>\n", "      <td>15.2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>6.004</td>\n", "      <td>17.10</td>\n", "      <td>15.2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>10</th>\n", "      <td>6.377</td>\n", "      <td>20.45</td>\n", "      <td>15.2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>11</th>\n", "      <td>6.009</td>\n", "      <td>13.27</td>\n", "      <td>15.2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>12</th>\n", "      <td>5.889</td>\n", "      <td>15.71</td>\n", "      <td>15.2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>13</th>\n", "      <td>5.949</td>\n", "      <td>8.26</td>\n", "      <td>21.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>14</th>\n", "      <td>6.096</td>\n", "      <td>10.26</td>\n", "      <td>21.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>15</th>\n", "      <td>5.834</td>\n", "      <td>8.47</td>\n", "      <td>21.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>16</th>\n", "      <td>5.935</td>\n", "      <td>6.58</td>\n", "      <td>21.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>17</th>\n", "      <td>5.990</td>\n", "      <td>14.67</td>\n", "      <td>21.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>18</th>\n", "      <td>5.456</td>\n", "      <td>11.69</td>\n", "      <td>21.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>19</th>\n", "      <td>5.727</td>\n", "      <td>11.28</td>\n", "      <td>21.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>20</th>\n", "      <td>5.570</td>\n", "      <td>21.02</td>\n", "      <td>21.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>21</th>\n", "      <td>5.965</td>\n", "      <td>13.83</td>\n", "      <td>21.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>22</th>\n", "      <td>6.142</td>\n", "      <td>18.72</td>\n", "      <td>21.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>23</th>\n", "      <td>5.813</td>\n", "      <td>19.88</td>\n", "      <td>21.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>24</th>\n", "      <td>5.924</td>\n", "      <td>16.30</td>\n", "      <td>21.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>25</th>\n", "      <td>5.599</td>\n", "      <td>16.51</td>\n", "      <td>21.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>26</th>\n", "      <td>5.813</td>\n", "      <td>14.81</td>\n", "      <td>21.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>27</th>\n", "      <td>6.047</td>\n", "      <td>17.28</td>\n", "      <td>21.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>28</th>\n", "      <td>6.495</td>\n", "      <td>12.80</td>\n", "      <td>21.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>29</th>\n", "      <td>6.674</td>\n", "      <td>11.98</td>\n", "      <td>21.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>459</th>\n", "      <td>6.484</td>\n", "      <td>18.68</td>\n", "      <td>20.2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>460</th>\n", "      <td>5.304</td>\n", "      <td>24.91</td>\n", "      <td>20.2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>461</th>\n", "      <td>6.185</td>\n", "      <td>18.03</td>\n", "      <td>20.2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>462</th>\n", "      <td>6.229</td>\n", "      <td>13.11</td>\n", "      <td>20.2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>463</th>\n", "      <td>6.242</td>\n", "      <td>10.74</td>\n", "      <td>20.2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>464</th>\n", "      <td>6.750</td>\n", "      <td>7.74</td>\n", "      <td>20.2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>465</th>\n", "      <td>7.061</td>\n", "      <td>7.01</td>\n", "      <td>20.2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>466</th>\n", "      <td>5.762</td>\n", "      <td>10.42</td>\n", "      <td>20.2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>467</th>\n", "      <td>5.871</td>\n", "      <td>13.34</td>\n", "      <td>20.2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>468</th>\n", "      <td>6.312</td>\n", "      <td>10.58</td>\n", "      <td>20.2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>469</th>\n", "      <td>6.114</td>\n", "      <td>14.98</td>\n", "      <td>20.2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>470</th>\n", "      <td>5.905</td>\n", "      <td>11.45</td>\n", "      <td>20.2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>471</th>\n", "      <td>5.454</td>\n", "      <td>18.06</td>\n", "      <td>20.1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>472</th>\n", "      <td>5.414</td>\n", "      <td>23.97</td>\n", "      <td>20.1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>473</th>\n", "      <td>5.093</td>\n", "      <td>29.68</td>\n", "      <td>20.1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>474</th>\n", "      <td>5.983</td>\n", "      <td>18.07</td>\n", "      <td>20.1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>475</th>\n", "      <td>5.983</td>\n", "      <td>13.35</td>\n", "      <td>20.1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>476</th>\n", "      <td>5.707</td>\n", "      <td>12.01</td>\n", "      <td>19.2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>477</th>\n", "      <td>5.926</td>\n", "      <td>13.59</td>\n", "      <td>19.2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>478</th>\n", "      <td>5.670</td>\n", "      <td>17.60</td>\n", "      <td>19.2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>479</th>\n", "      <td>5.390</td>\n", "      <td>21.14</td>\n", "      <td>19.2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>480</th>\n", "      <td>5.794</td>\n", "      <td>14.10</td>\n", "      <td>19.2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>481</th>\n", "      <td>6.019</td>\n", "      <td>12.92</td>\n", "      <td>19.2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>482</th>\n", "      <td>5.569</td>\n", "      <td>15.10</td>\n", "      <td>19.2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>483</th>\n", "      <td>6.027</td>\n", "      <td>14.33</td>\n", "      <td>19.2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>484</th>\n", "      <td>6.593</td>\n", "      <td>9.67</td>\n", "      <td>21.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>485</th>\n", "      <td>6.120</td>\n", "      <td>9.08</td>\n", "      <td>21.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>486</th>\n", "      <td>6.976</td>\n", "      <td>5.64</td>\n", "      <td>21.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>487</th>\n", "      <td>6.794</td>\n", "      <td>6.48</td>\n", "      <td>21.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>488</th>\n", "      <td>6.030</td>\n", "      <td>7.88</td>\n", "      <td>21.0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>489 rows × 3 columns</p>\n", "</div>"], "text/plain": ["        RM  LSTAT  PTRATIO\n", "0    6.575   4.98     15.3\n", "1    6.421   9.14     17.8\n", "2    7.185   4.03     17.8\n", "3    6.998   2.94     18.7\n", "4    7.147   5.33     18.7\n", "5    6.430   5.21     18.7\n", "6    6.012  12.43     15.2\n", "7    6.172  19.15     15.2\n", "8    5.631  29.93     15.2\n", "9    6.004  17.10     15.2\n", "10   6.377  20.45     15.2\n", "11   6.009  13.27     15.2\n", "12   5.889  15.71     15.2\n", "13   5.949   8.26     21.0\n", "14   6.096  10.26     21.0\n", "15   5.834   8.47     21.0\n", "16   5.935   6.58     21.0\n", "17   5.990  14.67     21.0\n", "18   5.456  11.69     21.0\n", "19   5.727  11.28     21.0\n", "20   5.570  21.02     21.0\n", "21   5.965  13.83     21.0\n", "22   6.142  18.72     21.0\n", "23   5.813  19.88     21.0\n", "24   5.924  16.30     21.0\n", "25   5.599  16.51     21.0\n", "26   5.813  14.81     21.0\n", "27   6.047  17.28     21.0\n", "28   6.495  12.80     21.0\n", "29   6.674  11.98     21.0\n", "..     ...    ...      ...\n", "459  6.484  18.68     20.2\n", "460  5.304  24.91     20.2\n", "461  6.185  18.03     20.2\n", "462  6.229  13.11     20.2\n", "463  6.242  10.74     20.2\n", "464  6.750   7.74     20.2\n", "465  7.061   7.01     20.2\n", "466  5.762  10.42     20.2\n", "467  5.871  13.34     20.2\n", "468  6.312  10.58     20.2\n", "469  6.114  14.98     20.2\n", "470  5.905  11.45     20.2\n", "471  5.454  18.06     20.1\n", "472  5.414  23.97     20.1\n", "473  5.093  29.68     20.1\n", "474  5.983  18.07     20.1\n", "475  5.983  13.35     20.1\n", "476  5.707  12.01     19.2\n", "477  5.926  13.59     19.2\n", "478  5.670  17.60     19.2\n", "479  5.390  21.14     19.2\n", "480  5.794  14.10     19.2\n", "481  6.019  12.92     19.2\n", "482  5.569  15.10     19.2\n", "483  6.027  14.33     19.2\n", "484  6.593   9.67     21.0\n", "485  6.120   9.08     21.0\n", "486  6.976   5.64     21.0\n", "487  6.794   6.48     21.0\n", "488  6.030   7.88     21.0\n", "\n", "[489 rows x 3 columns]"]}, "execution_count": 41, "metadata": {}, "output_type": "execute_result"}], "source": ["features"]}, {"cell_type": "code", "execution_count": 42, "metadata": {"scrolled": true}, "outputs": [{"data": {"text/plain": ["0      504000.0\n", "1      453600.0\n", "2      728700.0\n", "3      701400.0\n", "4      760200.0\n", "5      602700.0\n", "6      480900.0\n", "7      569100.0\n", "8      346500.0\n", "9      396900.0\n", "10     315000.0\n", "11     396900.0\n", "12     455700.0\n", "13     428400.0\n", "14     382200.0\n", "15     417900.0\n", "16     485100.0\n", "17     367500.0\n", "18     424200.0\n", "19     382200.0\n", "20     285600.0\n", "21     411600.0\n", "22     319200.0\n", "23     304500.0\n", "24     327600.0\n", "25     291900.0\n", "26     348600.0\n", "27     310800.0\n", "28     386400.0\n", "29     441000.0\n", "         ...   \n", "459    350700.0\n", "460    252000.0\n", "461    306600.0\n", "462    449400.0\n", "463    483000.0\n", "464    497700.0\n", "465    525000.0\n", "466    457800.0\n", "467    432600.0\n", "468    445200.0\n", "469    401100.0\n", "470    432600.0\n", "471    319200.0\n", "472    147000.0\n", "473    170100.0\n", "474    285600.0\n", "475    422100.0\n", "476    457800.0\n", "477    514500.0\n", "478    485100.0\n", "479    413700.0\n", "480    384300.0\n", "481    445200.0\n", "482    367500.0\n", "483    352800.0\n", "484    470400.0\n", "485    432600.0\n", "486    501900.0\n", "487    462000.0\n", "488    249900.0\n", "Name: MED<PERSON>, Length: 489, dtype: float64"]}, "execution_count": 42, "metadata": {}, "output_type": "execute_result"}], "source": ["prices"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Distribution of Data"]}, {"cell_type": "code", "execution_count": 43, "metadata": {}, "outputs": [{"data": {"image/png": "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\n", "text/plain": ["<matplotlib.figure.Figure at 0xd8663e80b8>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "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\n", "text/plain": ["<matplotlib.figure.Figure at 0xd8665ab5c0>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "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\n", "text/plain": ["<matplotlib.figure.Figure at 0xd8669b4780>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import matplotlib.pyplot as plt\n", "\n", "fig=plt.figure()\n", "ax=fig.add_subplot(1, 1, 1)\n", "ax.hist(data['RM'], bins = 15)  \n", "plt.title(\"Average number of rooms Distribution \")\n", "plt.xlabel(\"RM\")\n", "plt.ylabel(\"frequency\")\n", "plt.show()\n", "\n", "fig=plt.figure()\n", "ax=fig.add_subplot(1, 1, 1)\n", "ax.hist(data['LSTAT'], bins = 15)  \n", "plt.title(\"Homeowners distribution with low class\")\n", "plt.xlabel(\"LSTAT\")\n", "plt.ylabel(\"frequency\")\n", "plt.show()\n", "\n", "fig=plt.figure()\n", "ax=fig.add_subplot(1, 1, 1)\n", "ax.hist(data['PTRATIO'], bins = 15)  \n", "plt.title(\"Students to Teachers ratio distribution\")\n", "plt.xlabel(\"PTRATIO\")\n", "plt.ylabel(\"frequency\")\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Data Exploration\n", "In this first section of this project, you will make a cursory investigation about the Boston housing data and provide your observations. Familiarizing yourself with the data through an explorative process is a fundamental practice to help you better understand and justify your results.\n", "\n", "Since the main goal of this project is to construct a working model which has the capability of predicting the value of houses, we will need to separate the dataset into **features** and the **target variable**. The **features**, `'RM'`, `'LSTAT'`, and `'PTRATIO'`, give us quantitative information about each data point. The **target variable**, `'MEDV'`, will be the variable we seek to predict. These are stored in `features` and `prices`, respectively."]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Implementation: Calculate Statistics\n", "For your very first coding implementation, you will calculate descriptive statistics about the Boston housing prices. Since `numpy` has already been imported for you, use this library to perform the necessary calculations. These statistics will be extremely important later on to analyze various prediction results from the constructed model.\n", "\n", "In the code cell below, you will need to implement the following:\n", "- Calculate the minimum, maximum, mean, median, and standard deviation of `'MEDV'`, which is stored in `prices`.\n", "  - Store each calculation in their respective variable."]}, {"cell_type": "code", "execution_count": 44, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Statistics for Boston housing dataset:\n", "\n", "Minimum price: $105000.0\n", "Maximum price: $1024800.0\n", "Mean price: $454342.9447852761\n", "Median price $438900.0\n", "Standard deviation of prices: $165171.13154429477\n"]}], "source": ["# TODO: Minimum price of the data\n", "minimum_price = np.min(prices)\n", "\n", "# TODO: Maximum price of the data\n", "maximum_price = np.max(prices)\n", "\n", "# TODO: Mean price of the data\n", "mean_price = np.mean(prices)\n", "\n", "# TODO: Median price of the data\n", "median_price = np.median(prices)\n", "\n", "# TODO: Standard deviation of prices of the data\n", "std_price = np.std(prices)\n", "\n", "# Show the calculated statistics\n", "print(\"Statistics for Boston housing dataset:\\n\")\n", "print(\"Minimum price: ${}\".format(minimum_price)) \n", "print(\"Maximum price: ${}\".format(maximum_price))\n", "print(\"Mean price: ${}\".format(mean_price))\n", "print(\"Median price ${}\".format(median_price))\n", "print(\"Standard deviation of prices: ${}\".format(std_price))"]}, {"cell_type": "code", "execution_count": 45, "metadata": {}, "outputs": [{"data": {"image/png": "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\n", "text/plain": ["<matplotlib.figure.Figure at 0xd866fef320>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "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\n", "text/plain": ["<matplotlib.figure.Figure at 0xd86719d7f0>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "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\n", "text/plain": ["<matplotlib.figure.Figure at 0xd8672c2828>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["#                         RM VS PRICES\n", "fig=plt.figure()\n", "ax=fig.add_subplot(1, 1, 1)\n", "ax.scatter(data['RM'], data['MEDV']) \n", "#Lables & Title\n", "plt.title(\"Average selling Prices and Average number of rooms\")\n", "plt.xlabel(\"RM\")\n", "plt.ylabel(\"Prices\")\n", "plt.show()\n", "\n", "#                       LSTAT VS PRICES\n", "fig=plt.figure()\n", "ax=fig.add_subplot(1, 1, 1)\n", "ax.scatter(data['LSTAT'], data['MEDV'])  \n", "plt.title(\"Average selling Prices VS % of low class Homeowners\")\n", "plt.xlabel(\"LSTAT\")\n", "plt.ylabel(\"Prices\")\n", "plt.show()\n", "\n", "#                       PTRATIO VS PRICES\n", "fig=plt.figure()\n", "ax=fig.add_subplot(1, 1, 1)\n", "ax.scatter(data['PTRATIO'], data['MEDV'])  \n", "plt.title(\"Average selling Prices and Ratio of Students to Teachers\")\n", "plt.xlabel(\"PTRATIO\")\n", "plt.ylabel(\"Prices\")\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Question 1 - Feature Observation\n", "As a reminder, we are using three features from the Boston housing dataset: `'RM'`, `'LSTAT'`, and `'PTRATIO'`. For each data point (neighborhood):\n", "- `'RM'` is the average number of rooms among homes in the neighborhood.\n", "- `'LSTAT'` is the percentage of homeowners in the neighborhood considered \"lower class\" (working poor).\n", "- `'PTRATIO'` is the ratio of students to teachers in primary and secondary schools in the neighborhood.\n", "\n", "\n", "** Using your intuition, for each of the three features above, do you think that an increase in the value of that feature would lead to an **increase** in the value of `'MEDV'` or a **decrease** in the value of `'MEDV'`? Justify your answer for each.**\n", "\n", "**Hint:** This problem can phrased using examples like below.  \n", "* Would you expect a home that has an `'RM'` value(number of rooms) of 6 be worth more or less than a home that has an `'RM'` value of 7?\n", "* Would you expect a neighborhood that has an `'LSTAT'` value(percent of lower class workers) of 15 have home prices be worth more or less than a neighborhood that has an `'LSTAT'` value of 20?\n", "* Would you expect a neighborhood that has an `'PTRATIO'` value(ratio of students to teachers) of 10 have home prices be worth more or less than a neighborhood that has an `'PTRATIO'` value of 15?"]}, {"cell_type": "markdown", "metadata": {}, "source": ["**Answer: ** \n", "\n", "From the above scatterplots it is clear that:\n", "1. RM\n", "   - For a higher RM, one would expect to observe a higher MEDV.\n", "   - This is because more rooms would imply more space, thereby costing more, taking all other factors constant.\n", "2. LSTAT\n", "   - For a higher LSTAT, one would expect to observe a a lower MEDV.\n", "   - The social milieux in an area dominated by \"lower class\" citizens may not be conducive for young children. It may also be relatively unsafe compared to an area dominated by \"upper class\" citizens. Hence an area with more \"lower class\" citizens would lower demand, hence lower prices.\n", "3. PTRATIO\n", "   - For a higher PTRAITO, one would expect to observe a lower MEDV.\n", "   - This is because there would be a lower teacher-to-student ratio resulting in less attention dedicated to each student that may impair their performance in school. Typically this is the scenario in public/state schools compared to private schools. And the prices of houses around public schools are generally lower than those around private schools. Hence one would expect a lower price given a high student-to-teacher ratio due to a lower demand for houses in such areas."]}, {"cell_type": "markdown", "metadata": {}, "source": ["----\n", "\n", "## Developing a Model\n", "In this second section of the project, you will develop the tools and techniques necessary for a model to make a prediction. Being able to make accurate evaluations of each model's performance through the use of these tools and techniques helps to greatly reinforce the confidence in your predictions."]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Implementation: Define a Performance Metric\n", "It is difficult to measure the quality of a given model without quantifying its performance over training and testing. This is typically done using some type of performance metric, whether it is through calculating some type of error, the goodness of fit, or some other useful measurement. For this project, you will be calculating the [*coefficient of determination*](http://stattrek.com/statistics/dictionary.aspx?definition=coefficient_of_determination), R<sup>2</sup>, to quantify your model's performance. The coefficient of determination for a model is a useful statistic in regression analysis, as it often describes how \"good\" that model is at making predictions. \n", "\n", "The values for R<sup>2</sup> range from 0 to 1, which captures the percentage of squared correlation between the predicted and actual values of the **target variable**. A model with an R<sup>2</sup> of 0 is no better than a model that always predicts the *mean* of the target variable, whereas a model with an R<sup>2</sup> of 1 perfectly predicts the target variable. Any value between 0 and 1 indicates what percentage of the target variable, using this model, can be explained by the **features**. _A model can be given a negative R<sup>2</sup> as well, which indicates that the model is **arbitrarily worse** than one that always predicts the mean of the target variable._\n", "\n", "For the `performance_metric` function in the code cell below, you will need to implement the following:\n", "- Use `r2_score` from `sklearn.metrics` to perform a performance calculation between `y_true` and `y_predict`.\n", "- Assign the performance score to the `score` variable."]}, {"cell_type": "code", "execution_count": 46, "metadata": {}, "outputs": [], "source": ["# TODO: Import 'r2_score'\n", "from sklearn.metrics import r2_score\n", "def performance_metric(y_true, y_predict):\n", "    \"\"\" Calculates and returns the performance score between \n", "        true and predicted values based on the metric chosen. \"\"\"\n", "    \n", "    # TODO: Calculate the performance score between 'y_true' and 'y_predict'\n", "    score = r2_score(y_true,y_predict)\n", "    \n", "    # Return the score\n", "    return score"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Question 2 - Goodness of <PERSON><PERSON>\n", "Assume that a dataset contains five data points and a model made the following predictions for the target variable:\n", "\n", "| True Value | Prediction |\n", "| :-------------: | :--------: |\n", "| 3.0 | 2.5 |\n", "| -0.5 | 0.0 |\n", "| 2.0 | 2.1 |\n", "| 7.0 | 7.8 |\n", "| 4.2 | 5.3 |\n", "\n", "Run the code cell below to use the `performance_metric` function and calculate this model's coefficient of determination."]}, {"cell_type": "code", "execution_count": 47, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Model has a coefficient of determination, R^2, of 0.923.\n"]}], "source": ["# Calculate the performance of this model\n", "score = performance_metric([3, -0.5, 2, 7, 4.2], [2.5, 0.0, 2.1, 7.8, 5.3])\n", "print(\"Model has a coefficient of determination, R^2, of {:.3f}.\".format(score))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["* Would you consider this model to have successfully captured the variation of the target variable? \n", "* Why or why not?\n", "\n", "** Hint: **  The R2 score is the proportion of the variance in the dependent variable that is predictable from the independent variable. In other words:\n", "* R2 score of 0 means that the dependent variable cannot be predicted from the independent variable.\n", "* R2 score of 1 means the dependent variable can be predicted from the independent variable.\n", "* R2 score between 0 and 1 indicates the extent to which the dependent variable is predictable. An \n", "* R2 score of 0.40 means that 40 percent of the variance in Y is predictable from X."]}, {"cell_type": "markdown", "metadata": {}, "source": ["**Answer:**\n", "- R^2 = 92.3% \n", "- This implies 92.3% of variation is explained by the target variable and it seems to be high. The model has a fairly strong correlation and has successfully captured the variation of the target variable.\n", "- We only have five points here, and it may be hard to draw conclusion that is statistically significant. More datapoints could perhaps have helped to improve the model."]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Implementation: Shuffle and Split Data\n", "Your next implementation requires that you take the Boston housing dataset and split the data into training and testing subsets. Typically, the data is also shuffled into a random order when creating the training and testing subsets to remove any bias in the ordering of the dataset.\n", "\n", "For the code cell below, you will need to implement the following:\n", "- Use `train_test_split` from `sklearn.cross_validation` to shuffle and split the `features` and `prices` data into training and testing sets.\n", "  - Split the data into 80% training and 20% testing.\n", "  - Set the `random_state` for `train_test_split` to a value of your choice. This ensures results are consistent.\n", "- Assign the train and testing splits to `X_train`, `X_test`, `y_train`, and `y_test`."]}, {"cell_type": "code", "execution_count": 48, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Training and testing split was successful.\n"]}], "source": ["# TODO: Import 'train_test_split'\n", "from sklearn.cross_validation import train_test_split\n", "\n", "# TODO: Shuffle and split the data into training and testing subsets\n", "X_train, X_test, y_train, y_test = train_test_split(features, prices, test_size=0.2, random_state=0)\n", "\n", "# Success\n", "print(\"Training and testing split was successful.\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Question 3 - Training and Testing\n", "\n", "* What is the benefit to splitting a dataset into some ratio of training and testing subsets for a learning algorithm?\n", "\n", "**Hint:** Think about how overfitting or underfitting is contingent upon how splits on data is done."]}, {"cell_type": "markdown", "metadata": {}, "source": ["**Answer: **\n", "\n", "If we are building model and checking the performance of the model on the same data(which was used for building the model), our model will lead to ovefitting and it will perform worst on the unseen dataset.That's why We use the training data to train the model. And then we use the testing data to checking the performance of the model. It's important that two sets are independent from each other or result will be biased."]}, {"cell_type": "markdown", "metadata": {}, "source": ["----\n", "\n", "## Analyzing Model Performance\n", "In this third section of the project, you'll take a look at several models' learning and testing performances on various subsets of training data. Additionally, you'll investigate one particular algorithm with an increasing `'max_depth'` parameter on the full training set to observe how model complexity affects performance. Graphing your model's performance based on varying criteria can be beneficial in the analysis process, such as visualizing behavior that may not have been apparent from the results alone."]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Learning Curves\n", "The following code cell produces four graphs for a decision tree model with different maximum depths. Each graph visualizes the learning curves of the model for both training and testing as the size of the training set is increased. Note that the shaded region of a learning curve denotes the uncertainty of that curve (measured as the standard deviation). The model is scored on both the training and testing sets using R<sup>2</sup>, the coefficient of determination.  \n", "\n", "Run the code cell below and use these graphs to answer the following question."]}, {"cell_type": "code", "execution_count": 49, "metadata": {"scrolled": false}, "outputs": [{"data": {"image/png": "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\n", "text/plain": ["<matplotlib.figure.Figure at 0xd866e24dd8>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Produce learning curves for varying training set sizes and maximum depths\n", "vs.ModelLearning(features, prices)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Question 4 - Learning the Data\n", "* Choose one of the graphs above and state the maximum depth for the model. \n", "* What happens to the score of the training curve as more training points are added? What about the testing curve? \n", "* Would having more training points benefit the model? \n", "\n", "**Hint:** Are the learning curves converging to particular scores? Generally speaking, the more data you have, the better. But if your training and testing curves are converging with a score above your benchmark threshold, would this be necessary?\n", "Think about the pros and cons of adding more training points based on if the training and testing curves are converging."]}, {"cell_type": "markdown", "metadata": {}, "source": ["**Answer: **\n", "\n", "1. max_depth = 1 (<PERSON> <PERSON><PERSON>)\n", "   - We can see how the testing score (green line) increases with the number of observations.\n", "   - However, the testing score only increases to approximately 0.4, a low score. This indicates how the model does not generalize well for new, unseen, data.\n", "   - Moreover, the training score (red line) decreases with the number of observations. Also, the training score decreases to a very low score of approximately 0.4. This indicates how the model does not seem to fit the data well.\n", "   - Thus, we can say this model is facing a high bias problem. Consequently, having more training points would not benefit the model as the model is underfitting the dataset. Instead, one should increase the model complexity to better fit the dataset. Morever, the teting score has reached a plateau suggesting the model may not improve from adding more training points.\n", "\n", "2. max_depth = 3 (I<PERSON><PERSON> Scenario)\n", "   - Testing score (green line) increases with training points\n", "     - Reaches ~0.8\n", "     - High score\n", "     - Generalize well\n", "   - Training score (red line) decreases slightly with training points\n", "     - Reachers ~0.8\n", "     - High score\n", "     - Fit dataset well\n", "   - There seems to be no high bias or high variance problem\n", "     - Model fits and generalizes well\n", "     - Ideal\n", "     - More training points should help it become an even more ideal model!\n", "3. max_depth = 10 (<PERSON> <PERSON><PERSON><PERSON>)\n", "   - Testing score (green line) increases with training points\n", "     - Reaches ~0.7\n", "     - Not so high score\n", "     - Does not generalize well\n", "   - Training score (red line) barely decreases with training points\n", "     - At ~1.0\n", "     - Almost perfect score\n", "     - Overfitting dataset\n", "   - There seems to be a high variance problem\n", "     - Overfitting\n", "4. max_depth = 6 (<PERSON><PERSON><PERSON> <PERSON>)\n", "   - Testing score (green line) increases with training points\n", "     - Reaches ~0.75\n", "     - Not so high score\n", "     - Does not generalize well\n", "   - Training score (red line) decreases slightly with training points\n", "     - At ~0.9\n", "     - High score\n", "     - Overfitting dataset\n", "   - There seems to be a high variance problem\n", "     - Overfitting\n", "     - More training points might help\n", "     - This is getting close to the ideal scenario!"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Complexity Curves\n", "The following code cell produces a graph for a decision tree model that has been trained and validated on the training data using different maximum depths. The graph produces two complexity curves — one for training and one for validation. Similar to the **learning curves**, the shaded regions of both the complexity curves denote the uncertainty in those curves, and the model is scored on both the training and validation sets using the `performance_metric` function.  \n", "\n", "** Run the code cell below and use this graph to answer the following two questions Q5 and Q6. **"]}, {"cell_type": "code", "execution_count": 50, "metadata": {}, "outputs": [{"data": {"image/png": "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\n", "text/plain": ["<matplotlib.figure.Figure at 0xd86729fbe0>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["vs.ModelComplexity(X_train, y_train)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Question 5 - <PERSON><PERSON>-<PERSON><PERSON><PERSON>\n", "* When the model is trained with a maximum depth of 1, does the model suffer from high bias or from high variance? \n", "* How about when the model is trained with a maximum depth of 10? What visual cues in the graph justify your conclusions?\n", "\n", "**Hint:** High bias is a sign of underfitting(model is not complex enough to pick up the nuances in the data) and high variance is a sign of overfitting(model is by-hearting the data and cannot generalize well). Think about which model(depth 1 or 10) aligns with which part of the tradeoff."]}, {"cell_type": "markdown", "metadata": {}, "source": ["**Answer: **\n", "\n", "\n", "- It is easy to identify whether the model is suffering from a high bias or a high variance:\n", "    - High variance models have a gap between the training and validation scores.\n", "    - This is because it is able to fit the model well but unable to generalize well resulting in a high training score but low validation score.\n", "    - High bias models have have a small or no gap between the training and validations scores.\n", "    - This is because it is unable to fit the model well and unable to generalize well resulting in both scores converging to a similar low score.\n", "\n", "- **Maximum depth of 1: High Bias**\n", "\n", "    - Both training and testing scores are low.\n", "    - There is barely a gap between the training and testing scores.\n", "    - This indicates the model is not fitting the dataset well and not generalizing well hence the model is suffering from high bias.\n", "\n", "- **Maximum depth of 10: High Variance**\n", "\n", "    - Training score is high. Testing score is low\n", "    - There is a substantial gap between the training and testing scores.\n", "    - This indicates the model is fitting the dataset well but not generalizing well hence the model is suffering from high variance."]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Question 6 - Best-Guess Optimal Model\n", "* Which maximum depth do you think results in a model that best generalizes to unseen data? \n", "* What intuition lead you to this answer?\n", "\n", "** Hint: ** Look at the graph above Question 5 and see where the validation scores lie for the various depths that have been assigned to the model. Does it get better with increased depth? At what point do we get our best validation score without overcomplicating our model? And remember, Occams Razor states \"Among competing hypotheses, the one with the fewest assumptions should be selected.\""]}, {"cell_type": "markdown", "metadata": {}, "source": ["**Answer: **\n", "\n", "- The maximum depth of 4.\n", "- The training score seems to plateau here, indicating the highest possible score for the model's ability to generalize to unseen data.\n", "- Gap between the training score and testing score does not seem to be substantial too, indicating that the model may not be suffering from a high variance scenario.\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["-----\n", "\n", "## Evaluating Model Performance\n", "In this final section of the project, you will construct a model and make a prediction on the client's feature set using an optimized model from `fit_model`."]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Question 7 - <PERSON><PERSON> Search\n", "* What is the grid search technique?\n", "* How it can be applied to optimize a learning algorithm?\n", "\n", "** Hint: ** When explaining the Grid Search technique, be sure to touch upon why it is used,  what the 'grid' entails and what the end goal of this method is. To solidify your answer, you can also give an example of a parameter in a model that can be optimized using this approach."]}, {"cell_type": "markdown", "metadata": {}, "source": ["**Answer: **\n", "\n", " For a family of models with different values of parameters, grid search allows to select the best possible model for prediction by allowing us to specify which of those parameters we want to change, their corresponding rangues and the function score to be optimised. It then gives us a combination of values for those parameters that optimize the scoring function by searching each of those models iteratively.\n", "\n", "Grid search performs hyperparameter optimization by selecting a grid of values, evaluating them and returning the result. This parameter sweep functionality of grid search can optimise a learning algorithm."]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Question 8 - Cross-Validation\n", "\n", "* What is the k-fold cross-validation training technique? \n", "\n", "* What benefit does this technique provide for grid search when optimizing a model?\n", "\n", "**Hint:** When explaining the k-fold cross validation technique, be sure to touch upon what 'k' is, how the dataset is split into different parts for training and testing and the number of times it is run based on the 'k' value.\n", "\n", "When thinking about how k-fold cross validation helps grid search, think about the main drawbacks of grid search which are hinged upon **using a particular subset of data for training or testing** and how k-fold cv could help alleviate that. You can refer to the [docs](http://scikit-learn.org/stable/modules/cross_validation.html#cross-validation) for your answer."]}, {"cell_type": "markdown", "metadata": {}, "source": ["**Answer: **\n", "\n", "- **K-fold cross-validation summary:**\n", "  - Dataset is split into K \"folds\" of equal size.\n", "  - Each fold acts as the testing set 1 time, and acts as the training set K-1 times.\n", "  - Average testing performance is used as the estimate of out-of-sample performance.\n", "  - Also known as cross-validated performance.\n", "- **Benefits of k-fold cross-validation:**\n", "    - More reliable estimate of out-of-sample performance than train/test split.\n", "    - Reduce the variance of a single trial of a train/test split.\n", "    - Hence, with the benefits of k-fold cross-validation, we're able to use the average testing accuracy as a benchmark to decide which is the most optimal set of parameters for the learning algorithm.\n", "    - If we do not use a cross-validation set and we run grid-search, we would have different sets of optimal parameters due to the fact that without a cross-validation set, the estimate of out-of-sample performance would have a high variance.\n", "    - In summary, without k-fold cross-validation the risk is higher that grid search will select hyper-parameter value combinations that perform very well on a specific train-test split but poorly otherwise.\n", "\n", "- **Limitation of k-fold cross-validation:**\n", "    - *It does not work well when data is not uniformly distributed (e.g. sorted data).*"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Implementation: Fitting a Model\n", "Your final implementation requires that you bring everything together and train a model using the **decision tree algorithm**. To ensure that you are producing an optimized model, you will train the model using the grid search technique to optimize the `'max_depth'` parameter for the decision tree. The `'max_depth'` parameter can be thought of as how many questions the decision tree algorithm is allowed to ask about the data before making a prediction. Decision trees are part of a class of algorithms called *supervised learning algorithms*.\n", "\n", "In addition, you will find your implementation is using `ShuffleSplit()` for an alternative form of cross-validation (see the `'cv_sets'` variable). While it is not the K-Fold cross-validation technique you describe in **Question 8**, this type of cross-validation technique is just as useful!. The `ShuffleSplit()` implementation below will create 10 (`'n_splits'`) shuffled sets, and for each shuffle, 20% (`'test_size'`) of the data will be used as the *validation set*. While you're working on your implementation, think about the contrasts and similarities it has to the K-fold cross-validation technique.\n", "\n", "Please note that ShuffleSplit has different parameters in scikit-learn versions 0.17 and 0.18.\n", "For the `fit_model` function in the code cell below, you will need to implement the following:\n", "- Use [`DecisionTreeRegressor`](http://scikit-learn.org/stable/modules/generated/sklearn.tree.DecisionTreeRegressor.html) from `sklearn.tree` to create a decision tree regressor object.\n", "  - Assign this object to the `'regressor'` variable.\n", "- Create a dictionary for `'max_depth'` with the values from 1 to 10, and assign this to the `'params'` variable.\n", "- Use [`make_scorer`](http://scikit-learn.org/stable/modules/generated/sklearn.metrics.make_scorer.html) from `sklearn.metrics` to create a scoring function object.\n", "  - Pass the `performance_metric` function as a parameter to the object.\n", "  - Assign this scoring function to the `'scoring_fnc'` variable.\n", "- Use [`GridSearchCV`](http://scikit-learn.org/0.17/modules/generated/sklearn.grid_search.GridSearchCV.html) from `sklearn.grid_search` to create a grid search object.\n", "  - Pass the variables `'regressor'`, `'params'`, `'scoring_fnc'`, and `'cv_sets'` as parameters to the object. \n", "  - Assign the `GridSearchCV` object to the `'grid'` variable."]}, {"cell_type": "code", "execution_count": 55, "metadata": {}, "outputs": [], "source": ["# TODO: Import 'make_scorer', 'DecisionTreeRegressor', and 'GridSearchCV'\n", "from sklearn.metrics import make_scorer\n", "from sklearn.tree import DecisionTreeRegressor\n", "from sklearn.grid_search import GridSearchCV\n", "def fit_model(X, y):\n", "    \"\"\" Performs grid search over the 'max_depth' parameter for a \n", "        decision tree regressor trained on the input data [X, y]. \"\"\"\n", "    \n", "    # Create cross-validation sets from the training data\n", "    # sklearn version 0.18: ShuffleSplit(n_splits=10, test_size=0.1, train_size=None, random_state=None)\n", "    # sklearn versiin 0.17: ShuffleSplit(n, n_iter=10, test_size=0.1, train_size=None, random_state=None)\n", "    cv_sets = ShuffleSplit(X.shape[0], n_iter = 10, test_size = 0.20, random_state = 0)\n", "\n", "    # TODO: Create a decision tree regressor object\n", "    regressor = DecisionTreeRegressor(random_state=0)\n", "\n", "    # TODO: Create a dictionary for the parameter 'max_depth' with a range from 1 to 10\n", "    params = {'max_depth':list(range(1,11))}\n", "\n", "    # TODO: Transform 'performance_metric' into a scoring function using 'make_scorer' \n", "    scoring_fnc = make_scorer(performance_metric)\n", "\n", "    # TODO: Create the grid search cv object --> GridSearchCV()\n", "    # Make sure to include the right parameters in the object:\n", "    # (estimator, param_grid, scoring, cv) which have values 'regressor', 'params', 'scoring_fnc', and 'cv_sets' respectively.\n", "    grid = GridSearchCV(regressor, params, cv=cv_sets, scoring=scoring_fnc)\n", "\n", "    # Fit the grid search object to the data to compute the optimal model\n", "    grid = grid.fit(X, y)\n", "\n", "    # Return the optimal model after fitting the data\n", "    return grid.best_estimator_"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Making Predictions\n", "Once a model has been trained on a given set of data, it can now be used to make predictions on new sets of input data. In the case of a *decision tree regressor*, the model has learned *what the best questions to ask about the input data are*, and can respond with a prediction for the **target variable**. You can use these predictions to gain information about data where the value of the target variable is unknown — such as data the model was not trained on."]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Question 9 - Optimal Model\n", "\n", "* What maximum depth does the optimal model have? How does this result compare to your guess in **Question 6**?  \n", "\n", "Run the code block below to fit the decision tree regressor to the training data and produce an optimal model."]}, {"cell_type": "code", "execution_count": 56, "metadata": {"scrolled": true}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Parameter 'max_depth' is 4 for the optimal model.\n"]}], "source": ["# Fit the training data to the model using grid search\n", "reg = fit_model(X_train, y_train)\n", "\n", "# Produce the value for 'max_depth'\n", "print(\"Parameter 'max_depth' is {} for the optimal model.\".format(reg.get_params()['max_depth']))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["** Hint: ** The answer comes from the output of the code snipped above.\n", "\n", "**Answer: **\n", "\n", "The optimal model has a maximum depth of 4. The max_depth is the same as my guess in question 6.\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Question 10 - Predicting <PERSON><PERSON>\n", "Imagine that you were a real estate agent in the Boston area looking to use this model to help price homes owned by your clients that they wish to sell. You have collected the following information from three of your clients:\n", "\n", "| Feature | Client 1 | Client 2 | Client 3 |\n", "| :---: | :---: | :---: | :---: |\n", "| Total number of rooms in home | 5 rooms | 4 rooms | 8 rooms |\n", "| Neighborhood poverty level (as %) | 17% | 32% | 3% |\n", "| Student-teacher ratio of nearby schools | 15-to-1 | 22-to-1 | 12-to-1 |\n", "\n", "* What price would you recommend each client sell his/her home at? \n", "* Do these prices seem reasonable given the values for the respective features? \n", "\n", "**Hint:** Use the statistics you calculated in the **Data Exploration** section to help justify your response.  Of the three clients, client 3 has has the biggest house, in the best public school neighborhood with the lowest poverty level; while client 2 has the smallest house, in a neighborhood with a relatively high poverty rate and not the best public schools.\n", "\n", "Run the code block below to have your optimized model make predictions for each client's home."]}, {"cell_type": "code", "execution_count": 57, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Predicted selling price for Client 1's home: $391,183.33\n", "Predicted selling price for Client 2's home: $189,123.53\n", "Predicted selling price for Client 3's home: $942,666.67\n"]}], "source": ["# Produce a matrix for client data\n", "client_data = [[5, 17, 15], # Client 1\n", "               [4, 32, 22], # <PERSON><PERSON> 2\n", "               [8, 3, 12]]  # Client 3\n", "\n", "# Show predictions\n", "for i, price in enumerate(reg.predict(client_data)):\n", "    print(\"Predicted selling price for <PERSON><PERSON> {}'s home: ${:,.2f}\".format(i+1, price))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["**Answer: **\n", "\n", "- Data Exploration Findings\n", "\n", "Minimum price: $105,000.00\n", "\n", "Maximum price: $1,024,800.00\n", "\n", "Mean price: $454,342.94\n", "\n", "Median price $438,900.00\n", "\n", "Standard deviation of prices: $165,340.28\n", "\n", "### Explanation:\n", "\n", "1. Compared to the data-exploration, it seems that the houses' prices from client 1 and client 2 are below the mean and median prices.\n", "\n", "2. For client 2, it seems reasonable due to the high poverty level and student-to-teacher ratio.\n", "\n", "3. For client 1, it also seems reasonable due to the average poverty level and student-to-teacher ratio.\n", "\n", "4. And the house's price from client 3 is way above the mean and median prices, nearing to the maximum price in the dataset.\n", "This seems reasonable given the low poverty level and student-to-teacher ratio with a high number of rooms."]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Sensitivity\n", "An optimal model is not necessarily a robust model. Sometimes, a model is either too complex or too simple to sufficiently generalize to new data. Sometimes, a model could use a learning algorithm that is not appropriate for the structure of the data given. Other times, the data itself could be too noisy or contain too few samples to allow a model to adequately capture the target variable — i.e., the model is underfitted. \n", "\n", "**Run the code cell below to run the `fit_model` function ten times with different training and testing sets to see how the prediction for a specific client changes with respect to the data it's trained on.**"]}, {"cell_type": "code", "execution_count": 58, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Trial 1: $391,183.33\n", "Trial 2: $424,935.00\n", "Trial 3: $415,800.00\n", "Trial 4: $420,622.22\n", "Trial 5: $418,377.27\n", "Trial 6: $411,931.58\n", "Trial 7: $399,663.16\n", "Trial 8: $407,232.00\n", "Trial 9: $351,577.61\n", "Trial 10: $413,700.00\n", "\n", "Range in prices: $73,357.39\n"]}], "source": ["vs.PredictTrials(features, prices, fit_model, client_data)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Question 11 - Applicability\n", "\n", "* In a few sentences, discuss whether the constructed model should or should not be used in a real-world setting.  \n", "\n", "**Hint:** Take a look at the range in prices as calculated in the code snippet above. Some questions to answering:\n", "- How relevant today is data that was collected from 1978? How important is inflation?\n", "- Are the features present in the data sufficient to describe a home? Do you think factors like quality of apppliances in the home, square feet of the plot area, presence of pool or not etc should factor in?\n", "- Is the model robust enough to make consistent predictions?\n", "- Would data collected in an urban city like Boston be applicable in a rural city?\n", "- Is it fair to judge the price of an individual home based on the characteristics of the entire neighborhood?"]}, {"cell_type": "markdown", "metadata": {}, "source": ["**Answer: **\n", "\n", "- Data collected from a rural city may not be applicable as the demographics would change and other features may be better able to fit the dataset instead of a model with features that was learned using urban data.\n", "- The learning algorithm learned from a very old dataset that may not be relevant because demographics have changed a lot since 1978.\n", "- There are only 3 features currently, there are more features that can be included such as crime rates, nearby to city, public transport access and more."]}, {"cell_type": "markdown", "metadata": {}, "source": ["> **Note**: Once you have completed all of the code implementations and successfully answered each question above, you may finalize your work by exporting the iPython Notebook as an HTML document. You can do this by using the menu above and navigating to  \n", "**File -> Download as -> HTML (.html)**. Include the finished document along with this notebook as your submission."]}], "metadata": {"anaconda-cloud": {}, "kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.6.4"}}, "nbformat": 4, "nbformat_minor": 1}